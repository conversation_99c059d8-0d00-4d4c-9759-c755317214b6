"""
Simple accounts endpoints without response model validation
"""
from fastapi import APIRouter, Depends, HTTPException
from services.account_service import AccountService
from core.database import get_async_session
from pydantic import BaseModel, Field
from typing import List, Optional

router = APIRouter()


# Pydantic model for request
class AccountCreateRequest(BaseModel):
    username: str = Field(..., min_length=1, max_length=255)
    display_name: Optional[str] = Field(None, max_length=255)
    email: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=50)
    browser_profile_id: Optional[int] = None
    notes: Optional[str] = Field(None, max_length=1000)
    tags: Optional[List[str]] = None


async def get_account_service(session = Depends(get_async_session)) -> AccountService:
    """Get account service instance"""
    return AccountService(session)


@router.post("/simple")
async def create_account_simple(
    request: AccountCreateRequest,
    service: AccountService = Depends(get_account_service)
):
    """Create a new TikTok account - simple endpoint without response validation"""
    try:
        account = await service.create_account(
            username=request.username,
            display_name=request.display_name,
            email=request.email,
            phone=request.phone,
            browser_profile_id=request.browser_profile_id,
            notes=request.notes,
            tags=request.tags
        )
        # Return simple dict without any validation
        result = {
            "id": account.id,
            "username": account.username,
            "display_name": account.display_name,
            "email": account.email,
            "is_active": account.is_active,
            "is_logged_in": account.is_logged_in,
            "created_at": account.created_at.isoformat() if account.created_at else None
        }
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
