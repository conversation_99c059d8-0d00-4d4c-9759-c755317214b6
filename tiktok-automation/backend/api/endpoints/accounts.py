"""
TikTok Account management API endpoints
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Query, Body, Depends
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from services.account_service import AccountService
from services.cookie_service import CookieService
from models.tiktok_account import TikTokAccount
from core.database import get_async_session


def convert_account_to_response(account) -> dict:
    """Convert TikTokAccount model to response dict"""
    data = {
        'id': account.id,
        'username': account.username,
        'display_name': account.display_name,
        'email': account.email,
        'phone': account.phone,
        'is_active': account.is_active,
        'is_logged_in': account.is_logged_in,
        'is_verified': account.is_verified,
        'is_banned': account.is_banned,
        'profile_picture_url': account.profile_picture_url,
        'bio': account.bio,
        'follower_count': account.follower_count,
        'following_count': account.following_count,
        'likes_count': account.likes_count,
        'videos_count': account.videos_count,
        'browser_profile_id': account.browser_profile_id,
        'last_login': account.last_login.isoformat() if account.last_login else None,
        'login_count': account.login_count,
        'failed_login_attempts': account.failed_login_attempts,
        'daily_activity': getattr(account, 'daily_activity', {}),
        'engagement_rate': getattr(account, 'engagement_rate', 0.0),
        'warning_count': getattr(account, 'warning_count', 0),
        'last_warning': account.last_warning.isoformat() if getattr(account, 'last_warning', None) else None,
        'warning_message': getattr(account, 'warning_message', None),
        'notes': account.notes,
        'tags': getattr(account, 'tags', []),
        'created_at': account.created_at.isoformat() if account.created_at else None,
        'updated_at': account.updated_at.isoformat() if account.updated_at else None,
    }
    return data

router = APIRouter()

async def get_account_service() -> AccountService:
    return AccountService()

async def get_cookie_service() -> CookieService:
    return CookieService()


# Pydantic models for request/response
class AccountCreateRequest(BaseModel):
    username: str = Field(..., min_length=1, max_length=255)
    display_name: Optional[str] = Field(None, max_length=255)
    email: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=50)
    browser_profile_id: Optional[int] = None
    notes: Optional[str] = Field(None, max_length=1000)
    tags: Optional[List[str]] = None


class AccountUpdateRequest(BaseModel):
    username: Optional[str] = Field(None, min_length=1, max_length=255)
    display_name: Optional[str] = Field(None, max_length=255)
    email: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=50)
    browser_profile_id: Optional[int] = None
    is_active: Optional[bool] = None
    notes: Optional[str] = Field(None, max_length=1000)
    tags: Optional[List[str]] = None


# AccountResponse removed - using dict responses directly


class LoginRequest(BaseModel):
    manual_login: bool = True
    save_cookies: bool = True


@router.get("/")
async def get_accounts(
    active_only: bool = Query(False, description="Filter active accounts only"),
    logged_in_only: bool = Query(False, description="Filter logged in accounts only"),
    limit: Optional[int] = Query(None, ge=1, le=100, description="Limit number of results"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    service: AccountService = Depends(get_account_service)
):
    """Get list of TikTok accounts"""
    try:
        accounts = await service.get_accounts(
            active_only=active_only,
            logged_in_only=logged_in_only,
            limit=limit,
            offset=offset
        )
        return [account.to_dict() for account in accounts]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{account_id}")
async def get_account(
    account_id: int,
    service: AccountService = Depends(get_account_service)
):
    """Get account by ID"""
    try:
        account = await service.get_account(account_id)
        if not account:
            raise HTTPException(status_code=404, detail="Account not found")
        return account.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/")
async def create_account(
    request: AccountCreateRequest,
    service: AccountService = Depends(get_account_service)
):
    """Create a new TikTok account"""
    try:
        account = await service.create_account(
            username=request.username,
            display_name=request.display_name,
            email=request.email,
            phone=request.phone,
            browser_profile_id=request.browser_profile_id,
            notes=request.notes,
            tags=request.tags
        )
        return account.to_dict()
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{account_id}")
async def update_account(
    account_id: int,
    request: AccountUpdateRequest,
    service: AccountService = Depends(get_account_service)
):
    """Update account"""
    try:
        # Convert request to dict, excluding None values
        updates = {k: v for k, v in request.dict().items() if v is not None}

        account = await service.update_account(account_id, **updates)
        if not account:
            raise HTTPException(status_code=404, detail="Account not found")

        return account.to_dict()
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{account_id}")
async def delete_account(
    account_id: int,
    service: AccountService = Depends(get_account_service)
):
    """Delete account"""
    try:
        success = await service.delete_account(account_id)
        if not success:
            raise HTTPException(status_code=404, detail="Account not found")
        return {"message": "Account deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/login")
async def login_account(
    account_id: int,
    request: LoginRequest,
    service: AccountService = Depends(get_account_service)
):
    """Login to TikTok account"""
    try:
        result = await service.login_account(
            account_id=account_id,
            manual_login=request.manual_login,
            save_cookies=request.save_cookies
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/logout")
async def logout_account(
    account_id: int,
    service: AccountService = Depends(get_account_service)
):
    """Logout from TikTok account"""
    try:
        result = await service.logout_account(account_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{account_id}/login-status")
async def check_login_status(
    account_id: int,
    service: AccountService = Depends(get_account_service)
):
    """Check account login status"""
    try:
        result = await service.check_login_status(account_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/complete-login")
async def complete_login(
    account_id: int,
    service: AccountService = Depends(get_account_service)
):
    """Mark login as completed and save cookies"""
    try:
        result = await service.complete_login(account_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/refresh")
async def refresh_account_data(
    account_id: int,
    service: AccountService = Depends(get_account_service)
):
    """Refresh account data from TikTok"""
    try:
        result = await service.refresh_account_data(account_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics/summary")
async def get_account_statistics(
    service: AccountService = Depends(get_account_service)
):
    """Get account statistics"""
    try:
        stats = await service.get_account_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Cookie management endpoints
@router.get("/{account_id}/cookies")
async def get_account_cookies(
    account_id: int,
    cookie_service: CookieService = Depends(get_cookie_service)
):
    """Get account cookies (for debugging)"""
    try:
        cookies = await cookie_service.load_cookies(account_id)
        if not cookies:
            raise HTTPException(status_code=404, detail="No cookies found")

        # Return cookie count and domains for security
        cookie_info = {
            "count": len(cookies),
            "domains": list(set(cookie.get("domain", "") for cookie in cookies)),
            "has_session": any("session" in cookie.get("name", "").lower() for cookie in cookies)
        }
        return cookie_info
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{account_id}/cookies")
async def delete_account_cookies(
    account_id: int,
    cookie_service: CookieService = Depends(get_cookie_service)
):
    """Delete account cookies"""
    try:
        success = await cookie_service.delete_cookies(account_id)
        if not success:
            raise HTTPException(status_code=404, detail="Account not found")
        return {"message": "Cookies deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/cookies/validate")
async def validate_account_cookies(
    account_id: int,
    cookie_service: CookieService = Depends(get_cookie_service)
):
    """Validate account cookies"""
    try:
        result = await cookie_service.validate_cookies(account_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/cookies/export")
async def export_account_cookies(
    account_id: int,
    format: str = Body("json", embed=True),
    cookie_service: CookieService = Depends(get_cookie_service)
):
    """Export account cookies"""
    try:
        if format not in ["json", "netscape"]:
            raise HTTPException(status_code=400, detail="Format must be 'json' or 'netscape'")

        cookies_data = await cookie_service.export_cookies(account_id, format)
        if not cookies_data:
            raise HTTPException(status_code=404, detail="No cookies found")

        return {
            "format": format,
            "data": cookies_data,
            "filename": f"account_{account_id}_cookies.{format}"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{account_id}/cookies/import")
async def import_account_cookies(
    account_id: int,
    cookies_data: str = Body(..., embed=True),
    format: str = Body("json", embed=True),
    cookie_service: CookieService = Depends(get_cookie_service)
):
    """Import account cookies"""
    try:
        if format not in ["json", "netscape"]:
            raise HTTPException(status_code=400, detail="Format must be 'json' or 'netscape'")

        success = await cookie_service.import_cookies(account_id, cookies_data, format)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to import cookies")

        return {"message": "Cookies imported successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cookies/cleanup")
async def cleanup_expired_cookies(
    cookie_service: CookieService = Depends(get_cookie_service)
):
    """Cleanup expired cookies from all accounts"""
    try:
        result = await cookie_service.cleanup_expired_cookies()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
