"""
Main API router
"""

from fastapi import APIRouter

from .endpoints import profiles, proxies, accounts, accounts_simple, tasks, system, competitors, monitoring, antidetect, automation

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(profiles.router, prefix="/profiles", tags=["profiles"])
api_router.include_router(proxies.router, prefix="/proxies", tags=["proxies"])
api_router.include_router(accounts.router, prefix="/accounts", tags=["accounts"])
api_router.include_router(accounts_simple.router, prefix="/accounts-simple", tags=["accounts-simple"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["tasks"])
api_router.include_router(competitors.router, prefix="/competitors", tags=["competitors"])
api_router.include_router(system.router, prefix="/system", tags=["system"])
api_router.include_router(monitoring.router, prefix="/monitoring", tags=["monitoring"])
api_router.include_router(antidetect.router, prefix="/antidetect", tags=["antidetect"])
api_router.include_router(automation.router, prefix="/automation", tags=["automation"])
