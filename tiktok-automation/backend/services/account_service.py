"""
TikTok Account Service for managing accounts and authentication
"""

from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload
from loguru import logger

from models.tiktok_account import TikTokAccount
from models.browser_profile import Browser<PERSON><PERSON>file
from models.proxy import Proxy
from services.cookie_service import CookieService
from camoufox_integration.browser_manager import BrowserManager
from core.database import get_async_session, get_async_session_context


class AccountService:
    """Service for managing TikTok accounts"""
    
    def __init__(self):
        self.cookie_service = CookieService()
        self.browser_manager = BrowserManager()
    
    async def create_account(
        self,
        username: str,
        display_name: Optional[str] = None,
        email: Optional[str] = None,
        phone: Optional[str] = None,
        browser_profile_id: Optional[int] = None,
        notes: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> TikTokAccount:
        """Create a new TikTok account"""
        
        async with get_async_session_context() as session:
            try:
                # Check if username already exists
                existing = await session.execute(
                    select(TikTokAccount).where(TikTokAccount.username == username)
                )
                if existing.scalar_one_or_none():
                    raise ValueError(f"Account with username '{username}' already exists")
                
                # Validate browser profile if provided
                if browser_profile_id:
                    profile = await session.get(BrowserProfile, browser_profile_id)
                    if not profile:
                        raise ValueError(f"Browser profile {browser_profile_id} not found")
                
                # Create account
                account = TikTokAccount(
                    username=username,
                    display_name=display_name,
                    email=email,
                    phone=phone,
                    browser_profile_id=browser_profile_id,
                    notes=notes,
                    tags=tags or []
                )
                
                session.add(account)
                await session.commit()
                await session.refresh(account)
                
                logger.info(f"Created TikTok account: {account.username}")
                return account
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error creating account: {e}")
                raise
    
    async def get_account(self, account_id: int) -> Optional[TikTokAccount]:
        """Get account by ID"""

        async with get_async_session_context() as session:
            try:
                result = await session.execute(
                    select(TikTokAccount)
                    .options(selectinload(TikTokAccount.browser_profile))
                    .where(TikTokAccount.id == account_id)
                )
                return result.scalar_one_or_none()
            except Exception as e:
                logger.error(f"Error getting account {account_id}: {e}")
                return None
    
    async def get_accounts(
        self,
        active_only: bool = False,
        logged_in_only: bool = False,
        limit: Optional[int] = None,
        offset: int = 0
    ) -> List[TikTokAccount]:
        """Get list of accounts"""

        async with get_async_session_context() as session:
            try:
                query = select(TikTokAccount).options(
                    selectinload(TikTokAccount.browser_profile)
                )
                
                if active_only:
                    query = query.where(TikTokAccount.is_active == True)
                
                if logged_in_only:
                    query = query.where(TikTokAccount.is_logged_in == True)
                
                query = query.offset(offset)
                if limit:
                    query = query.limit(limit)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(f"Error getting accounts: {e}")
                return []
    
    async def update_account(
        self,
        account_id: int,
        **updates
    ) -> Optional[TikTokAccount]:
        """Update account"""

        async with get_async_session_context() as session:
            try:
                account = await session.get(TikTokAccount, account_id)
                if not account:
                    return None
                
                # Update fields
                for key, value in updates.items():
                    if hasattr(account, key):
                        setattr(account, key, value)
                
                await session.commit()
                await session.refresh(account)
                
                logger.info(f"Updated account: {account.username}")
                return account
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error updating account {account_id}: {e}")
                raise
    
    async def delete_account(self, account_id: int) -> bool:
        """Delete account"""

        async with get_async_session_context() as session:
            try:
                account = await session.get(TikTokAccount, account_id)
                if not account:
                    return False
                
                # Delete associated cookies
                await self.cookie_service.delete_cookies(account_id)
                
                # Delete account
                await session.delete(account)
                await session.commit()
                
                logger.info(f"Deleted account: {account.username}")
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error deleting account {account_id}: {e}")
                raise
    
    async def login_account(
        self,
        account_id: int,
        manual_login: bool = True,
        save_cookies: bool = True
    ) -> Dict[str, Any]:
        """Login to TikTok account"""
        
        try:
            account = await self.get_account(account_id)
            if not account:
                return {"success": False, "error": "Account not found"}
            
            # Get browser profile
            profile = account.browser_profile
            if not profile:
                return {"success": False, "error": "No browser profile assigned"}
            
            # Get proxy if assigned
            proxy = None
            if profile.proxy_id:
                async with get_async_session_context() as session:
                    proxy = await session.get(Proxy, profile.proxy_id)
            
            # Launch browser
            browser = await self.browser_manager.create_browser_instance(
                profile=profile,
                proxy=proxy,
                headless=False  # Show browser for manual login
            )
            
            context = await self.browser_manager.create_browser_context(browser, profile, proxy)
            page = await context.new_page()
            
            # Try to load existing cookies first
            existing_cookies = await self.cookie_service.load_cookies(account_id)
            if existing_cookies:
                await context.add_cookies(existing_cookies)
                logger.info(f"Loaded existing cookies for {account.username}")
            
            # Navigate to TikTok
            await page.goto("https://www.tiktok.com/login", timeout=30000)
            
            if manual_login:
                # Start manual login process
                logger.info(f"Manual login started for account: {account.username}")
                logger.info("Please complete login manually in the browser, then call /complete-login endpoint")

                # Don't wait for completion here - let user control the process
                # Just return success to indicate browser is ready for manual login
                login_success = True

                # Don't cleanup browser - keep it open for user interaction
                # The browser will be cleaned up when complete_login is called

                return {
                    "success": True,
                    "message": "Browser opened for manual login. Please complete login and call /complete-login endpoint.",
                    "account_id": account_id,
                    "next_step": "Call POST /api/v1/accounts/{account_id}/complete-login after completing login"
                }
            else:
                # Automated login (to be implemented)
                login_success = False
                logger.warning("Automated login not implemented yet")

                # Cleanup for automated login failure
                await page.close()
                await self.browser_manager.close_context(context)
                await self.browser_manager.close_browser(browser)

                return {
                    "success": False,
                    "message": "Automated login not implemented yet",
                    "cookies_saved": False
                }
            
        except Exception as e:
            logger.error(f"Error logging in account {account_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def logout_account(self, account_id: int) -> Dict[str, Any]:
        """Logout from TikTok account"""
        
        try:
            # Delete cookies
            success = await self.cookie_service.delete_cookies(account_id)
            
            if success:
                # Update account status
                async with get_async_session_context() as session:
                    account = await session.get(TikTokAccount, account_id)
                    if account:
                        account.is_logged_in = False
                        await session.commit()
                
                return {"success": True, "message": "Logged out successfully"}
            else:
                return {"success": False, "error": "Failed to logout"}
                
        except Exception as e:
            logger.error(f"Error logging out account {account_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def check_login_status(self, account_id: int) -> Dict[str, Any]:
        """Check if account is logged in"""
        
        try:
            # Validate cookies
            result = await self.cookie_service.validate_cookies(account_id)
            
            # Update account status based on validation
            async with get_async_session_context() as session:
                account = await session.get(TikTokAccount, account_id)
                if account:
                    account.is_logged_in = result.get("valid", False)
                    await session.commit()
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking login status for account {account_id}: {e}")
            return {"valid": False, "error": str(e)}

    async def _check_login_status(self, page) -> bool:
        """Check if user is logged in on TikTok page"""

        try:
            # Wait for page to load
            await page.wait_for_load_state("networkidle", timeout=10000)

            # Check for login indicators
            login_indicators = [
                '[data-e2e="profile-icon"]',  # Profile icon
                '[data-e2e="nav-profile"]',   # Profile nav
                '.avatar',                    # Avatar element
                '[href*="/profile"]',         # Profile link
                'button[data-e2e="top-profile-avatar"]',  # Top profile avatar
                '[data-e2e="upload-icon"]'    # Upload icon (logged in users)
            ]

            for selector in login_indicators:
                try:
                    element = await page.wait_for_selector(selector, timeout=2000)
                    if element:
                        logger.debug(f"Login indicator found: {selector}")
                        return True
                except:
                    continue

            # Additional check: look for user menu or profile elements
            try:
                # Check if we can find any user-specific elements
                user_elements = await page.query_selector_all('[data-e2e*="profile"], [data-e2e*="avatar"], .avatar')
                if user_elements:
                    return True
            except:
                pass

            return False

        except Exception as e:
            logger.error(f"Error checking login status: {e}")
            return False

    async def complete_login(self, account_id: int) -> Dict[str, Any]:
        """Complete login process - save cookies and update status"""

        try:
            # Get account
            async with get_async_session_context() as session:
                account = await session.get(TikTokAccount, account_id)
                if not account:
                    return {"success": False, "error": "Account not found"}

            # Get browser profile
            profile = None
            if account.browser_profile_id:
                async with get_async_session_context() as session:
                    profile = await session.get(BrowserProfile, account.browser_profile_id)

            if not profile:
                return {"success": False, "error": "Browser profile not found"}

            # Create browser instance to get cookies
            browser = await self.browser_manager.create_browser_instance(
                profile=profile,
                headless=False
            )

            context = await self.browser_manager.create_browser_context(
                browser, profile, None
            )

            page = await context.new_page()

            # Navigate to TikTok to check current status
            await page.goto("https://www.tiktok.com/", timeout=30000)

            # Check if actually logged in
            is_logged_in = await self._check_login_status(page)

            if is_logged_in:
                # Get current cookies
                cookies = await context.cookies()

                # Save cookies
                cookie_saved = await self.cookie_service.save_cookies(account_id, cookies)

                if cookie_saved:
                    # Update account status
                    account.update_login_status(True)
                    async with get_async_session_context() as session:
                        session.add(account)
                        await session.commit()

                    logger.info(f"Login completed and cookies saved for account: {account.username}")

                    # Cleanup
                    await page.close()
                    await self.browser_manager.close_context(context)
                    await self.browser_manager.close_browser(browser)

                    return {
                        "success": True,
                        "message": "Login completed successfully",
                        "cookies_saved": True,
                        "account_id": account_id
                    }
                else:
                    # Cleanup
                    await page.close()
                    await self.browser_manager.close_context(context)
                    await self.browser_manager.close_browser(browser)

                    return {
                        "success": False,
                        "error": "Failed to save cookies",
                        "cookies_saved": False
                    }
            else:
                # Cleanup
                await page.close()
                await self.browser_manager.close_context(context)
                await self.browser_manager.close_browser(browser)

                return {
                    "success": False,
                    "error": "User is not logged in. Please complete login first.",
                    "logged_in": False
                }

        except Exception as e:
            logger.error(f"Error completing login for account {account_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_account_statistics(self) -> Dict[str, Any]:
        """Get account statistics"""

        async with get_async_session_context() as session:
            try:
                accounts = await session.execute(select(TikTokAccount))
                all_accounts = accounts.scalars().all()
                
                stats = {
                    "total": len(all_accounts),
                    "active": 0,
                    "logged_in": 0,
                    "verified": 0,
                    "banned": 0,
                    "with_cookies": 0,
                    "total_followers": 0,
                    "total_following": 0,
                    "avg_engagement_rate": 0
                }
                
                total_engagement = 0
                accounts_with_followers = 0
                
                for account in all_accounts:
                    if account.is_active:
                        stats["active"] += 1
                    if account.is_logged_in:
                        stats["logged_in"] += 1
                    if account.is_verified:
                        stats["verified"] += 1
                    if account.is_banned:
                        stats["banned"] += 1
                    if account.cookies_data:
                        stats["with_cookies"] += 1
                    
                    stats["total_followers"] += account.follower_count
                    stats["total_following"] += account.following_count
                    
                    if account.follower_count > 0:
                        total_engagement += account.engagement_rate
                        accounts_with_followers += 1
                
                if accounts_with_followers > 0:
                    stats["avg_engagement_rate"] = total_engagement / accounts_with_followers
                
                return stats
                
            except Exception as e:
                logger.error(f"Error getting account statistics: {e}")
                return {}
    
    async def refresh_account_data(self, account_id: int) -> Dict[str, Any]:
        """Refresh account data from TikTok"""
        
        try:
            account = await self.get_account(account_id)
            if not account:
                return {"success": False, "error": "Account not found"}
            
            # Check if logged in
            if not account.is_logged_in:
                return {"success": False, "error": "Account not logged in"}
            
            # Get browser profile and proxy
            profile = account.browser_profile
            if not profile:
                return {"success": False, "error": "No browser profile assigned"}
            
            proxy = None
            if profile.proxy_id:
                async with get_async_session_context() as session:
                    proxy = await session.get(Proxy, profile.proxy_id)
            
            # Launch browser with cookies
            browser = await self.browser_manager.create_browser_instance(
                profile=profile,
                proxy=proxy,
                headless=True
            )
            
            context = await self.browser_manager.create_browser_context(browser, profile, proxy)
            
            # Load cookies
            cookies = await self.cookie_service.load_cookies(account_id)
            if cookies:
                await context.add_cookies(cookies)
            
            page = await context.new_page()
            
            # Navigate to profile page
            await page.goto(f"https://www.tiktok.com/@{account.username}", timeout=30000)
            
            # Extract profile data
            profile_data = await self._extract_profile_data(page)
            
            # Update account with new data
            if profile_data:
                account.update_profile_stats(profile_data)
                async with get_async_session() as session:
                    session.add(account)
                    await session.commit()
            
            # Cleanup
            await page.close()
            await self.browser_manager.close_context(context)
            await self.browser_manager.close_browser(browser)
            
            return {
                "success": True,
                "data": profile_data,
                "message": "Account data refreshed successfully"
            }
            
        except Exception as e:
            logger.error(f"Error refreshing account data for {account_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _extract_profile_data(self, page) -> Optional[Dict[str, Any]]:
        """Extract profile data from TikTok page"""
        
        try:
            # Wait for page to load
            await page.wait_for_load_state("networkidle", timeout=10000)
            
            # Extract data using JavaScript
            profile_data = await page.evaluate("""
                () => {
                    const data = {};
                    
                    // Try to find follower count
                    const followerElement = document.querySelector('[data-e2e="followers-count"]');
                    if (followerElement) {
                        data.followers = parseInt(followerElement.textContent.replace(/[^0-9]/g, '')) || 0;
                    }
                    
                    // Try to find following count
                    const followingElement = document.querySelector('[data-e2e="following-count"]');
                    if (followingElement) {
                        data.following = parseInt(followingElement.textContent.replace(/[^0-9]/g, '')) || 0;
                    }
                    
                    // Try to find likes count
                    const likesElement = document.querySelector('[data-e2e="likes-count"]');
                    if (likesElement) {
                        data.likes = parseInt(likesElement.textContent.replace(/[^0-9]/g, '')) || 0;
                    }
                    
                    // Try to find video count
                    const videoElements = document.querySelectorAll('[data-e2e="user-post-item"]');
                    data.videos = videoElements.length;
                    
                    // Try to find display name
                    const nameElement = document.querySelector('[data-e2e="user-title"]');
                    if (nameElement) {
                        data.display_name = nameElement.textContent.trim();
                    }
                    
                    // Try to find bio
                    const bioElement = document.querySelector('[data-e2e="user-bio"]');
                    if (bioElement) {
                        data.bio = bioElement.textContent.trim();
                    }
                    
                    // Check if verified
                    const verifiedElement = document.querySelector('[data-e2e="user-verified"]');
                    data.is_verified = !!verifiedElement;
                    
                    return data;
                }
            """)
            
            return profile_data
            
        except Exception as e:
            logger.error(f"Error extracting profile data: {e}")
            return None
