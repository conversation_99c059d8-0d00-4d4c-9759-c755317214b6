import React, { useState, useEffect } from 'react';
import { FiPlus, FiRefresh<PERSON>w, FiSearch, FiFilter } from 'react-icons/fi';

// Components
import AccountTable from '../components/Accounts/AccountTable';
import AccountForm from '../components/Accounts/AccountForm';
import EditAccountForm from '../components/Accounts/EditAccountForm';
import DeleteConfirmDialog from '../components/Dashboard/DeleteConfirmDialog';

// Services
import accountService from '../services/accountService';

const Accounts = () => {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [deletingAccount, setDeletingAccount] = useState(null);

  // Load accounts on component mount
  useEffect(() => {
    loadAccounts();
    
    // Listen for account updates
    const handleAccountUpdate = (event, data) => {
      switch (event) {
        case 'account_created':
        case 'account_updated':
        case 'login_completed':
        case 'login_started':
          loadAccounts(); // Refresh the list
          break;
        case 'account_deleted':
          setAccounts(prev => prev.filter(a => a.id !== data));
          break;
        default:
          break;
      }
    };

    accountService.addListener(handleAccountUpdate);

    return () => {
      accountService.removeListener(handleAccountUpdate);
    };
  }, []);

  const loadAccounts = async () => {
    try {
      setLoading(true);
      setError(null);
      const accountsData = await accountService.getAccounts();
      setAccounts(accountsData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle account actions
  const handleAccountAction = async (accountId, action) => {
    try {
      let result;

      switch (action) {
        case 'login':
          result = await accountService.startLogin(accountId);
          if (result.success) {
            alert('Browser đã mở để đăng nhập. Vui lòng hoàn tất đăng nhập và nhấn "Hoàn tất đăng nhập".');
          }
          break;
        case 'complete':
          result = await accountService.completeLogin(accountId);
          if (result.success) {
            alert('Đăng nhập hoàn tất! Cookies đã được lưu.');
          }
          break;
        case 'refresh':
          result = await accountService.checkLoginStatus(accountId);
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      if (result && !result.success) {
        alert(`Lỗi: ${result.error || result.message}`);
      }
    } catch (error) {
      console.error('Account action error:', error);
      alert(`Lỗi: ${error.message}`);
    }
  };

  const handleCreateAccount = async (accountData) => {
    try {
      await accountService.createAccount(accountData);
      setShowCreateForm(false);
      alert('Tạo account thành công!');
    } catch (error) {
      alert(`Lỗi tạo account: ${error.message}`);
    }
  };

  const handleUpdateAccount = async (accountId, updates) => {
    try {
      await accountService.updateAccount(accountId, updates);
      setEditingAccount(null);
      alert('Cập nhật account thành công!');
    } catch (error) {
      alert(`Lỗi cập nhật account: ${error.message}`);
    }
  };

  const handleDeleteAccount = async () => {
    if (!deletingAccount) return;

    try {
      await accountService.deleteAccount(deletingAccount.id);
      setDeletingAccount(null);
      alert('Xóa account thành công!');
    } catch (error) {
      alert(`Lỗi xóa account: ${error.message}`);
    }
  };

  // Filter accounts
  const filteredAccounts = accounts.filter(account => {
    const matchesSearch = account.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (account.display_name && account.display_name.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'logged_in' && account.is_logged_in) ||
                         (statusFilter === 'not_logged_in' && !account.is_logged_in) ||
                         (statusFilter === 'active' && account.is_active) ||
                         (statusFilter === 'inactive' && !account.is_active);
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Đang tải accounts...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">TikTok Accounts</h1>
          <p className="text-gray-600">Quản lý tài khoản TikTok</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadAccounts}
            className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <FiRefreshCw className="w-4 h-4 mr-2" />
            Làm mới
          </button>
          <button
            onClick={() => setShowCreateForm(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <FiPlus className="w-4 h-4 mr-2" />
            Thêm Account
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Tìm kiếm theo username hoặc tên hiển thị..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <FiFilter className="text-gray-400" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tất cả</option>
              <option value="logged_in">Đã đăng nhập</option>
              <option value="not_logged_in">Chưa đăng nhập</option>
              <option value="active">Hoạt động</option>
              <option value="inactive">Không hoạt động</option>
            </select>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Accounts Table */}
      <div className="bg-white rounded-lg shadow-sm border">
        <AccountTable
          accounts={filteredAccounts}
          onAccountAction={handleAccountAction}
          onEditAccount={setEditingAccount}
          onDeleteAccount={setDeletingAccount}
        />
      </div>

      {/* Create Account Form */}
      {showCreateForm && (
        <AccountForm
          onSubmit={handleCreateAccount}
          onCancel={() => setShowCreateForm(false)}
        />
      )}

      {/* Edit Account Form */}
      {editingAccount && (
        <EditAccountForm
          account={editingAccount}
          onSubmit={(updates) => handleUpdateAccount(editingAccount.id, updates)}
          onCancel={() => setEditingAccount(null)}
        />
      )}

      {/* Delete Confirmation */}
      {deletingAccount && (
        <DeleteConfirmDialog
          title="Xóa Account"
          message={`Bạn có chắc chắn muốn xóa account "${deletingAccount.username}"? Hành động này không thể hoàn tác.`}
          onConfirm={handleDeleteAccount}
          onCancel={() => setDeletingAccount(null)}
        />
      )}
    </div>
  );
};

export default Accounts;
