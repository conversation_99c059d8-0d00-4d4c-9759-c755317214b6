/**
 * Error handling utilities for API responses and general errors
 */

/**
 * Standard error structure
 */
export class ApiError extends Error {
  constructor(message, status = null, code = null, details = null) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

/**
 * Handle API errors and convert them to a standard format
 * @param {Error|Object} error - The error object from API call
 * @returns {ApiError} Standardized error object
 */
export function handleApiError(error) {
  // If it's already an ApiError, return as is
  if (error instanceof ApiError) {
    return error;
  }

  // Handle Axios errors
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    const message = data?.detail || data?.message || data?.error || 'Server error occurred';
    const code = data?.code || null;
    const details = data?.details || null;
    
    return new ApiError(message, status, code, details);
  } else if (error.request) {
    // Request was made but no response received
    return new ApiError('Network error - no response from server', null, 'NETWORK_ERROR');
  } else {
    // Something else happened
    return new ApiError(error.message || 'Unknown error occurred', null, 'UNKNOWN_ERROR');
  }
}

/**
 * Handle general errors (non-API)
 * @param {Error} error - The error object
 * @param {string} context - Context where the error occurred
 * @returns {Object} Error information object
 */
export function handleGeneralError(error, context = 'Unknown') {
  console.error(`Error in ${context}:`, error);
  
  return {
    message: error.message || 'An unexpected error occurred',
    context,
    timestamp: new Date().toISOString(),
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
  };
}

/**
 * Get user-friendly error message
 * @param {Error|ApiError} error - The error object
 * @returns {string} User-friendly error message
 */
export function getUserFriendlyMessage(error) {
  if (error instanceof ApiError) {
    switch (error.status) {
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'Authentication required. Please log in and try again.';
      case 403:
        return 'You do not have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 409:
        return 'Conflict occurred. The resource may already exist.';
      case 422:
        return error.message || 'Invalid data provided.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Server error occurred. Please try again later.';
      case 502:
      case 503:
      case 504:
        return 'Service temporarily unavailable. Please try again later.';
      default:
        return error.message || 'An error occurred. Please try again.';
    }
  }
  
  return error.message || 'An unexpected error occurred.';
}

/**
 * Log error for debugging purposes
 * @param {Error} error - The error object
 * @param {string} context - Context where the error occurred
 * @param {Object} additionalInfo - Additional information to log
 */
export function logError(error, context = 'Unknown', additionalInfo = {}) {
  const errorInfo = {
    message: error.message,
    name: error.name,
    context,
    timestamp: new Date().toISOString(),
    ...additionalInfo
  };

  if (error instanceof ApiError) {
    errorInfo.status = error.status;
    errorInfo.code = error.code;
    errorInfo.details = error.details;
  }

  if (process.env.NODE_ENV === 'development') {
    errorInfo.stack = error.stack;
  }

  console.error('Error logged:', errorInfo);
  
  // In production, you might want to send this to an error tracking service
  // Example: sendToErrorTrackingService(errorInfo);
}

/**
 * Retry function with exponential backoff
 * @param {Function} fn - Function to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise} Result of the function or throws last error
 */
export async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      // Exponential backoff: baseDelay * 2^attempt
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * Validate response data
 * @param {Object} data - Response data to validate
 * @param {Array} requiredFields - Required fields that must be present
 * @throws {ApiError} If validation fails
 */
export function validateResponseData(data, requiredFields = []) {
  if (!data || typeof data !== 'object') {
    throw new ApiError('Invalid response data format', null, 'INVALID_RESPONSE');
  }
  
  for (const field of requiredFields) {
    if (!(field in data)) {
      throw new ApiError(`Missing required field: ${field}`, null, 'MISSING_FIELD');
    }
  }
}

/**
 * Create error boundary handler for React components
 * @param {string} componentName - Name of the component
 * @returns {Function} Error handler function
 */
export function createErrorBoundaryHandler(componentName) {
  return (error, errorInfo) => {
    const errorDetails = {
      component: componentName,
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString()
    };
    
    logError(error, `React Error Boundary - ${componentName}`, errorDetails);
  };
}

// Default export for convenience
export default {
  ApiError,
  handleApiError,
  handleGeneralError,
  getUserFriendlyMessage,
  logError,
  retryWithBackoff,
  validateResponseData,
  createErrorBoundaryHandler
};
