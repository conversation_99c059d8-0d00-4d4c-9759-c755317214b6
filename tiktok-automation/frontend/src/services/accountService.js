import { accountAPI } from './api';
import { handleApiError } from '../utils/errorHandler';

class AccountService {
  constructor() {
    this.accounts = [];
    this.listeners = [];
  }

  // Event handling
  addListener(callback) {
    this.listeners.push(callback);
  }

  removeListener(callback) {
    this.listeners = this.listeners.filter(l => l !== callback);
  }

  notifyListeners(event, data) {
    this.listeners.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        console.error('Error in account service listener:', error);
      }
    });
  }

  // Account management
  async getAccounts() {
    try {
      const response = await accountAPI.getAccounts();
      this.accounts = response.accounts || response || [];
      return this.accounts;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to fetch accounts:', apiError);
      throw new Error(apiError.message);
    }
  }

  async getAccount(accountId) {
    try {
      const account = await accountAPI.getAccount(accountId);
      return account;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to fetch account:', apiError);
      throw new Error(apiError.message);
    }
  }

  async createAccount(accountData) {
    try {
      const account = await accountAPI.createAccount(accountData);
      
      // Add to local accounts list
      this.accounts.push(account);
      this.notifyListeners('account_created', account);
      
      return account;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to create account:', apiError);
      throw new Error(apiError.message);
    }
  }

  async updateAccount(accountId, updates) {
    try {
      const account = await accountAPI.updateAccount(accountId, updates);
      
      // Update local accounts list
      const accountIndex = this.accounts.findIndex(a => a.id === accountId);
      if (accountIndex !== -1) {
        this.accounts[accountIndex] = account;
      }
      
      this.notifyListeners('account_updated', account);
      return account;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to update account:', apiError);
      throw new Error(apiError.message);
    }
  }

  async deleteAccount(accountId) {
    try {
      await accountAPI.deleteAccount(accountId);
      
      // Remove from local accounts list
      this.accounts = this.accounts.filter(a => a.id !== accountId);
      this.notifyListeners('account_deleted', accountId);
      
      return true;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to delete account:', apiError);
      throw new Error(apiError.message);
    }
  }

  // Login management
  async startLogin(accountId) {
    try {
      const result = await accountAPI.login(accountId);

      if (result.success) {
        // Update local account status
        const accountIndex = this.accounts.findIndex(a => a.id === accountId);
        if (accountIndex !== -1) {
          this.accounts[accountIndex].status = 'ĐANG ĐĂNG NHẬP';
          this.accounts[accountIndex].current_action = 'Đang đăng nhập TikTok...';
        }

        this.notifyListeners('login_started', accountId);
      }

      return result;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to start login:', apiError);
      throw new Error(apiError.message);
    }
  }

  async completeLogin(accountId) {
    try {
      const result = await accountAPI.completeLogin(accountId);

      if (result.success) {
        // Update local account status
        const accountIndex = this.accounts.findIndex(a => a.id === accountId);
        if (accountIndex !== -1) {
          this.accounts[accountIndex].is_logged_in = true;
          this.accounts[accountIndex].status = 'SẴN SÀNG';
          this.accounts[accountIndex].current_action = 'Sẵn sàng hoạt động';
        }

        this.notifyListeners('login_completed', accountId);
      }

      return result;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to complete login:', apiError);
      throw new Error(apiError.message);
    }
  }

  async checkLoginStatus(accountId) {
    try {
      const result = await accountAPI.checkLoginStatus(accountId);
      
      // Update local account status
      const accountIndex = this.accounts.findIndex(a => a.id === accountId);
      if (accountIndex !== -1) {
        this.accounts[accountIndex].is_logged_in = result.valid;
      }
      
      return result;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Failed to check login status:', apiError);
      throw new Error(apiError.message);
    }
  }

  // Utility methods
  getAccountById(accountId) {
    return this.accounts.find(a => a.id === accountId);
  }

  getLoggedInAccounts() {
    return this.accounts.filter(a => a.is_logged_in);
  }

  getAccountsByStatus(status) {
    return this.accounts.filter(a => a.status === status);
  }

  // Status mapping
  mapStatusToVietnamese(status) {
    const statusMap = {
      'active': 'HOẠT ĐỘNG',
      'inactive': 'KHÔNG HOẠT ĐỘNG',
      'logging_in': 'ĐANG ĐĂNG NHẬP',
      'ready': 'SẴN SÀNG',
      'error': 'LỖI',
      'banned': 'BỊ KHÓA'
    };
    
    return statusMap[status] || status.toUpperCase();
  }

  getAvailableActions(account) {
    const actions = [];
    
    if (!account.is_logged_in) {
      actions.push('login');
    } else if (account.status === 'ĐANG ĐĂNG NHẬP') {
      actions.push('complete');
    } else if (account.is_logged_in && account.status === 'SẴN SÀNG') {
      actions.push('start', 'refresh');
    }
    
    return actions;
  }
}

// Create singleton instance
const accountService = new AccountService();

export default accountService;
