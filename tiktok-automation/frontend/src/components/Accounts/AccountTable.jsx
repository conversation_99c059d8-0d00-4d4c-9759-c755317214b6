import React from 'react';
import { <PERSON>Edit2, <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, FiRefreshC<PERSON> } from 'react-icons/fi';

const AccountTable = ({ accounts, onAccountAction, onEditAccount, onDeleteAccount }) => {
  const getStatusBadge = (account) => {
    if (account.is_banned) {
      return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">BỊ KHÓA</span>;
    }
    if (account.is_logged_in) {
      return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">ĐÃ ĐĂNG NHẬP</span>;
    }
    return <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">CHƯA ĐĂNG NHẬP</span>;
  };

  const getActivityBadge = (account) => {
    if (account.is_active) {
      return <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">HOẠT ĐỘNG</span>;
    }
    return <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">KHÔNG HOẠT ĐỘNG</span>;
  };

  const renderActionButtons = (account) => {
    const buttons = [];
    
    if (!account.is_logged_in) {
      // Hiển thị cả 2 buttons khi chưa logged in
      buttons.push(
        <button
          key="login"
          onClick={() => onAccountAction(account.id, 'login')}
          className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors"
          title="Bắt đầu đăng nhập"
        >
          Đăng nhập
        </button>
      );
      
      buttons.push(
        <button
          key="complete"
          onClick={() => onAccountAction(account.id, 'complete')}
          className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 transition-colors"
          title="Hoàn tất đăng nhập và lưu cookies"
        >
          Hoàn tất đăng nhập
        </button>
      );
    } else {
      // Khi đã logged in, hiển thị refresh và logout
      buttons.push(
        <button
          key="refresh"
          onClick={() => onAccountAction(account.id, 'refresh')}
          className="bg-gray-600 text-white px-3 py-1 rounded text-xs hover:bg-gray-700 transition-colors"
          title="Kiểm tra trạng thái đăng nhập"
        >
          <FiRefreshCw className="w-3 h-3" />
        </button>
      );
      
      buttons.push(
        <button
          key="logout"
          onClick={() => onAccountAction(account.id, 'logout')}
          className="bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700 transition-colors"
          title="Đăng xuất"
        >
          Đăng xuất
        </button>
      );
    }
    
    return (
      <div className="flex space-x-2">
        {buttons}
      </div>
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Chưa có';
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  if (accounts.length === 0) {
    return (
      <div className="text-center py-12">
        <FiUser className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Không có accounts</h3>
        <p className="mt-1 text-sm text-gray-500">Bắt đầu bằng cách tạo account TikTok đầu tiên.</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Account
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Trạng thái
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Thống kê
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Đăng nhập
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Hành động
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Quản lý
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {accounts.map((account) => (
            <tr key={account.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10">
                    {account.profile_picture_url ? (
                      <img
                        className="h-10 w-10 rounded-full"
                        src={account.profile_picture_url}
                        alt={account.username}
                      />
                    ) : (
                      <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                        <FiUser className="h-6 w-6 text-gray-600" />
                      </div>
                    )}
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900">
                      @{account.username}
                    </div>
                    <div className="text-sm text-gray-500">
                      {account.display_name || 'Chưa có tên hiển thị'}
                    </div>
                    {account.email && (
                      <div className="text-xs text-gray-400">
                        {account.email}
                      </div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="space-y-1">
                  {getStatusBadge(account)}
                  {getActivityBadge(account)}
                  {account.is_verified && (
                    <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                      <FiCheck className="inline w-3 h-3 mr-1" />
                      ĐÃ XÁC MINH
                    </span>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div className="space-y-1">
                  <div>Followers: {formatNumber(account.follower_count)}</div>
                  <div>Following: {formatNumber(account.following_count)}</div>
                  <div>Likes: {formatNumber(account.likes_count)}</div>
                  <div>Videos: {formatNumber(account.videos_count)}</div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div className="space-y-1">
                  <div>Lần cuối: {formatDate(account.last_login)}</div>
                  <div>Số lần: {account.login_count}</div>
                  {account.failed_login_attempts > 0 && (
                    <div className="text-red-600">
                      Thất bại: {account.failed_login_attempts}
                    </div>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {renderActionButtons(account)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div className="flex space-x-2">
                  <button
                    onClick={() => onEditAccount(account)}
                    className="text-indigo-600 hover:text-indigo-900"
                    title="Chỉnh sửa"
                  >
                    <FiEdit2 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => onDeleteAccount(account)}
                    className="text-red-600 hover:text-red-900"
                    title="Xóa"
                  >
                    <FiTrash2 className="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default AccountTable;
